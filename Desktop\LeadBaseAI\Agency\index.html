<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LeadBaseAI - AI Automation & Lead Generation</title>
    <script src="https://cdn.jsdelivr.net/npm/react@18.2.0/umd/react.production.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/react-dom@18.2.0/umd/react-dom.production.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@babel/standalone@7.22.5/babel.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/gsap@3.12.2/dist/gsap.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/gsap@3.12.2/dist/ScrollTrigger.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            color: #1e293b;
            overflow-x: hidden;
        }

        /* Light blue theme */
        .primary-blue { color: #3b82f6; }
        .light-blue { color: #60a5fa; }
        .bg-primary-blue { background-color: #3b82f6; }
        .bg-light-blue { background-color: #60a5fa; }

        /* Glow effects with light blue */
        .blue-glow {
            box-shadow: 0 0 20px rgba(59, 130, 246, 0.3), 0 0 40px rgba(59, 130, 246, 0.1);
            border: 1px solid rgba(59, 130, 246, 0.3);
        }
        .blue-glow:hover {
            box-shadow: 0 0 30px rgba(59, 130, 246, 0.6), 0 0 60px rgba(59, 130, 246, 0.2);
            transform: translateY(-2px);
            transition: all 0.3s ease;
        }

        /* Light gradient backgrounds */
        .light-gradient {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);
        }
        .blue-gradient {
            background: linear-gradient(135deg, #3b82f6 0%, #60a5fa 100%);
        }
        .light-blue-gradient {
            background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
        }

        /* Subtle grid background */
        .grid-bg {
            background-image:
                linear-gradient(rgba(59, 130, 246, 0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(59, 130, 246, 0.1) 1px, transparent 1px);
            background-size: 30px 30px;
        }

        /* Floating particles */
        .particle {
            position: absolute;
            background: rgba(59, 130, 246, 0.4);
            border-radius: 50%;
            animation: float 8s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-15px) rotate(180deg); }
        }

        /* Navigation styles */
        .nav-link {
            position: relative;
            color: #475569;
            transition: all 0.3s ease;
            font-weight: 500;
        }
        .nav-link:hover {
            color: #3b82f6;
        }
        .nav-link::after {
            content: '';
            position: absolute;
            bottom: -4px;
            left: 0;
            width: 0;
            height: 2px;
            background: linear-gradient(90deg, #3b82f6, #60a5fa);
            transition: width 0.3s ease;
        }
        .nav-link:hover::after {
            width: 100%;
        }

        /* Card hover effects */
        .hover-card {
            transition: all 0.3s ease;
            border: 1px solid rgba(59, 130, 246, 0.1);
            background: white;
        }
        .hover-card:hover {
            transform: translateY(-8px);
            border-color: rgba(59, 130, 246, 0.3);
            box-shadow: 0 20px 40px rgba(59, 130, 246, 0.1);
        }

        /* Button styles */
        .btn-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #60a5fa 100%);
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3);
        }

        .btn-outline {
            border: 2px solid #3b82f6;
            color: #3b82f6;
            padding: 10px 22px;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
            background: transparent;
        }
        .btn-outline:hover {
            background: #3b82f6;
            color: white;
            transform: translateY(-2px);
        }
    </style>
</head>
<body className="light-gradient">
    <div id="root"></div>
    <script type="text/babel">
        const { useEffect, useRef, useState } = React;

        // Nav Component with Light Blue Theme
        const Nav = () => {
            const [isOpen, setIsOpen] = useState(false);
            const navRef = useRef(null);

            useEffect(() => {
                gsap.from(navRef.current.children, {
                    y: -20,
                    opacity: 0,
                    stagger: 0.1,
                    duration: 0.6,
                    ease: 'power3.out'
                });
            }, []);

            return (
                <nav className="fixed top-0 w-full bg-white/95 backdrop-blur-xl z-50 py-4 shadow-lg border-b border-blue-100" ref={navRef}>
                    <div className="container mx-auto px-6 flex justify-between items-center">
                        <div className="flex items-center space-x-4">
                            <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl flex items-center justify-center blue-glow">
                                <svg className="w-7 h-7 text-white" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M13 3L4 14h7v7l9-11h-7V3z" />
                                </svg>
                            </div>
                            <div>
                                <span className="text-2xl font-bold text-gray-800">LeadBaseAI</span>
                                <div className="text-xs text-blue-500 font-medium">AI AUTOMATION & LEADS</div>
                            </div>
                        </div>

                        <div className="hidden md:flex space-x-8">
                            <a href="#automation" className="nav-link text-sm font-medium">Automation</a>
                            <a href="#leads" className="nav-link text-sm font-medium">Lead Generation</a>
                            <a href="#technology" className="nav-link text-sm font-medium">Technology</a>
                            <a href="#results" className="nav-link text-sm font-medium">Results</a>
                            <a href="#contact" className="nav-link text-sm font-medium">Contact</a>
                        </div>

                        <div className="hidden md:block">
                            <button className="btn-primary">
                                Get Started
                            </button>
                        </div>

                        <div className="md:hidden">
                            <button onClick={() => setIsOpen(!isOpen)} className="text-blue-500">
                                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                                </svg>
                            </button>
                        </div>
                    </div>

                    {isOpen && (
                        <div className="md:hidden bg-white/98 border-t border-blue-100 p-4">
                            <div className="flex flex-col space-y-4">
                                <a href="#automation" className="nav-link text-sm">Automation</a>
                                <a href="#leads" className="nav-link text-sm">Lead Generation</a>
                                <a href="#technology" className="nav-link text-sm">Technology</a>
                                <a href="#results" className="nav-link text-sm">Results</a>
                                <a href="#contact" className="nav-link text-sm">Contact</a>
                                <button className="btn-primary w-fit">
                                    Get Started
                                </button>
                            </div>
                        </div>
                    )}
                </nav>
            );
        };

        // Hero Component for AI Automation & Lead Generation
        const Hero = () => {
            const heroRef = useRef(null);

            useEffect(() => {
                gsap.from(heroRef.current.children, {
                    opacity: 0,
                    y: 50,
                    stagger: 0.2,
                    duration: 1,
                    ease: 'power3.out'
                });

                // Create floating particles
                const createParticles = () => {
                    for (let i = 0; i < 30; i++) {
                        const particle = document.createElement('div');
                        particle.className = 'particle';
                        particle.style.width = `${Math.random() * 6 + 3}px`;
                        particle.style.height = particle.style.width;
                        particle.style.left = `${Math.random() * 100}%`;
                        particle.style.top = `${Math.random() * 100}%`;
                        particle.style.animationDelay = `${Math.random() * 8}s`;
                        particle.style.animationDuration = `${Math.random() * 6 + 6}s`;
                        heroRef.current.appendChild(particle);
                    }
                };
                createParticles();
            }, []);

            return (
                <section className="min-h-screen flex items-center relative overflow-hidden pt-20" ref={heroRef}>
                    <div className="absolute inset-0 grid-bg opacity-30"></div>

                    <div className="container mx-auto px-6 relative z-10">
                        <div className="grid lg:grid-cols-2 gap-12 items-center">
                            <div className="space-y-8">
                                <div className="space-y-6">
                                    <div className="inline-block bg-blue-50 text-blue-600 px-4 py-2 rounded-full text-sm font-medium">
                                        🚀 AI-Powered Business Growth
                                    </div>
                                    <h1 className="text-5xl lg:text-6xl font-bold leading-tight text-gray-900">
                                        Scale Your Business with
                                        <span className="bg-gradient-to-r from-blue-500 to-blue-600 bg-clip-text text-transparent"> AI Automation</span> &
                                        <span className="bg-gradient-to-r from-blue-500 to-blue-600 bg-clip-text text-transparent">Lead Generation</span>
                                    </h1>
                                </div>

                                <p className="text-xl text-gray-600 leading-relaxed max-w-2xl">
                                    Transform your business with intelligent automation workflows and AI-powered lead generation systems.
                                    Get more qualified leads, automate repetitive tasks, and scale faster than ever before.
                                </p>

                                <div className="flex flex-col sm:flex-row gap-4">
                                    <button className="btn-primary flex items-center justify-center text-lg px-8 py-4">
                                        <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M13 3L4 14h7v7l9-11h-7V3z" />
                                        </svg>
                                        Start Free Trial
                                    </button>
                                    <button className="btn-outline text-lg px-8 py-4">
                                        Watch Demo
                                    </button>
                                </div>

                                <div className="flex items-center space-x-8 text-sm">
                                    <div className="flex items-center space-x-2">
                                        <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                                        <span className="text-gray-600">10,000+ Leads Generated</span>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                                        <span className="text-gray-600">500+ Automations Active</span>
                                    </div>
                                </div>
                            </div>

                            <div className="relative">
                                <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-3xl p-8 blue-glow">
                                    <div className="grid grid-cols-2 gap-6">
                                        <div className="bg-white rounded-xl p-6 hover-card">
                                            <div className="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center mb-4">
                                                <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                                                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
                                                </svg>
                                            </div>
                                            <h3 className="font-semibold text-gray-800 mb-2">AI Lead Scoring</h3>
                                            <p className="text-sm text-gray-600">Automatically qualify and prioritize leads</p>
                                        </div>

                                        <div className="bg-white rounded-xl p-6 hover-card">
                                            <div className="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center mb-4">
                                                <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                                                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" />
                                                </svg>
                                            </div>
                                            <h3 className="font-semibold text-gray-800 mb-2">Smart Automation</h3>
                                            <p className="text-sm text-gray-600">Workflow automation that learns and adapts</p>
                                        </div>

                                        <div className="bg-white rounded-xl p-6 hover-card">
                                            <div className="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center mb-4">
                                                <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                                                    <path d="M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z" />
                                                </svg>
                                            </div>
                                            <h3 className="font-semibold text-gray-800 mb-2">Real-time Analytics</h3>
                                            <p className="text-sm text-gray-600">Track performance and optimize campaigns</p>
                                        </div>

                                        <div className="bg-white rounded-xl p-6 hover-card">
                                            <div className="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center mb-4">
                                                <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                                                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
                                                </svg>
                                            </div>
                                            <h3 className="font-semibold text-gray-800 mb-2">CRM Integration</h3>
                                            <p className="text-sm text-gray-600">Seamless integration with your existing tools</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            );
        };

        // Automation Solutions Component
        const Automation = () => {
            const automationRef = useRef(null);
            useEffect(() => {
                gsap.from(automationRef.current.children, {
                    opacity: 0,
                    y: 60,
                    stagger: 0.2,
                    duration: 1,
                    ease: 'power3.out',
                    scrollTrigger: { trigger: automationRef.current, start: 'top 80%' }
                });
            }, []);

            const automationSolutions = [
                {
                    title: 'Smart Workflow Automation',
                    desc: 'Automate repetitive tasks, data entry, and complex business processes with AI that learns and adapts to your workflow patterns.',
                    icon: '⚙️',
                    features: ['Email Automation', 'Data Processing', 'Task Scheduling'],
                    savings: '80% time saved'
                },
                {
                    title: 'AI Lead Qualification',
                    desc: 'Automatically score, qualify, and route leads using advanced AI algorithms that understand your ideal customer profile.',
                    icon: '🎯',
                    features: ['Lead Scoring', 'Auto-Routing', 'Behavioral Analysis'],
                    savings: '300% more qualified leads'
                },
                {
                    title: 'Intelligent CRM Management',
                    desc: 'Keep your CRM updated automatically with AI-powered data enrichment, contact management, and pipeline optimization.',
                    icon: '📊',
                    features: ['Data Enrichment', 'Pipeline Management', 'Contact Updates'],
                    savings: '90% data accuracy'
                }
            ];

            return (
                <section id="automation" className="py-24 bg-white">
                    <div className="container mx-auto px-6">
                        <div className="text-center mb-16" ref={automationRef}>
                            <div className="inline-block bg-blue-50 text-blue-600 px-4 py-2 rounded-full text-sm font-medium mb-4">
                                🤖 AI Automation Solutions
                            </div>
                            <h2 className="text-4xl lg:text-5xl font-bold mb-6 text-gray-900">
                                Automate Everything with <span className="text-blue-500">Intelligent AI</span>
                            </h2>
                            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                                Transform your business operations with AI-powered automation that handles the work while you focus on growth.
                            </p>
                        </div>

                        <div className="grid lg:grid-cols-3 gap-8">
                            {automationSolutions.map((solution, index) => (
                                <div key={index} className="hover-card bg-white p-8 rounded-2xl shadow-lg">
                                    <div className="flex items-center justify-between mb-6">
                                        <span className="text-4xl">{solution.icon}</span>
                                        <div className="text-blue-500 text-sm bg-blue-50 px-3 py-1 rounded-full font-medium">
                                            {solution.savings}
                                        </div>
                                    </div>

                                    <h3 className="text-2xl font-bold mb-4 text-gray-900">
                                        {solution.title}
                                    </h3>

                                    <p className="text-gray-600 mb-6 leading-relaxed">
                                        {solution.desc}
                                    </p>

                                    <div className="space-y-2 mb-6">
                                        {solution.features.map((feature, idx) => (
                                            <div key={idx} className="flex items-center text-sm text-gray-600">
                                                <svg className="w-4 h-4 text-blue-500 mr-2" fill="currentColor" viewBox="0 0 24 24">
                                                    <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z" />
                                                </svg>
                                                {feature}
                                            </div>
                                        ))}
                                    </div>

                                    <button className="w-full btn-outline">
                                        Learn More
                                    </button>
                                </div>
                            ))}
                        </div>

                        <div className="text-center mt-16">
                            <button className="btn-primary text-lg px-10 py-4">
                                Start Automating Today
                            </button>
                        </div>
                    </div>
                </section>
            );
        };

        // Lead Generation Component
        const LeadGeneration = () => {
            const leadsRef = useRef(null);
            useEffect(() => {
                gsap.from(leadsRef.current.children, {
                    opacity: 0,
                    x: -50,
                    stagger: 0.2,
                    duration: 1,
                    ease: 'power3.out',
                    scrollTrigger: { trigger: leadsRef.current, start: 'top 80%' }
                });
            }, []);

            const leadStrategies = [
                {
                    title: 'AI-Powered Outreach',
                    desc: 'Personalized email campaigns that adapt to each prospect, increasing response rates by up to 400%.',
                    icon: '📧',
                    results: '400% higher response rates'
                },
                {
                    title: 'Smart Lead Magnets',
                    desc: 'AI-generated content and lead magnets that attract your ideal customers and capture high-quality leads.',
                    icon: '🧲',
                    results: '250% more lead captures'
                },
                {
                    title: 'Predictive Lead Scoring',
                    desc: 'Machine learning algorithms that identify your hottest prospects before your competitors do.',
                    icon: '🔥',
                    results: '60% faster conversions'
                }
            ];

            return (
                <section id="leads" className="py-24 bg-gradient-to-br from-blue-50 to-indigo-50">
                    <div className="container mx-auto px-6">
                        <div className="grid lg:grid-cols-2 gap-12 items-center">
                            <div ref={leadsRef}>
                                <div className="inline-block bg-blue-100 text-blue-600 px-4 py-2 rounded-full text-sm font-medium mb-6">
                                    🎯 Lead Generation Engine
                                </div>
                                <h2 className="text-4xl lg:text-5xl font-bold mb-6 text-gray-900">
                                    Generate More <span className="text-blue-500">Qualified Leads</span> with AI
                                </h2>
                                <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                                    Our AI-powered lead generation system finds, qualifies, and nurtures prospects automatically,
                                    delivering a steady stream of high-quality leads to your sales team.
                                </p>

                                <div className="space-y-6">
                                    {leadStrategies.map((strategy, index) => (
                                        <div key={index} className="flex items-start space-x-4">
                                            <div className="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center flex-shrink-0">
                                                <span className="text-xl">{strategy.icon}</span>
                                            </div>
                                            <div>
                                                <h3 className="text-xl font-semibold text-gray-900 mb-2">{strategy.title}</h3>
                                                <p className="text-gray-600 mb-2">{strategy.desc}</p>
                                                <div className="text-blue-500 font-medium text-sm">{strategy.results}</div>
                                            </div>
                                        </div>
                                    ))}
                                </div>

                                <div className="mt-8">
                                    <button className="btn-primary text-lg px-8 py-4">
                                        Start Generating Leads
                                    </button>
                                </div>
                            </div>

                            <div className="relative">
                                <div className="bg-white rounded-3xl p-8 shadow-2xl">
                                    <div className="text-center mb-6">
                                        <h3 className="text-2xl font-bold text-gray-900 mb-2">Lead Generation Dashboard</h3>
                                        <p className="text-gray-600">Real-time performance metrics</p>
                                    </div>

                                    <div className="grid grid-cols-2 gap-4 mb-6">
                                        <div className="bg-blue-50 rounded-xl p-4 text-center">
                                            <div className="text-2xl font-bold text-blue-600">1,247</div>
                                            <div className="text-sm text-gray-600">Leads This Month</div>
                                        </div>
                                        <div className="bg-green-50 rounded-xl p-4 text-center">
                                            <div className="text-2xl font-bold text-green-600">68%</div>
                                            <div className="text-sm text-gray-600">Qualification Rate</div>
                                        </div>
                                        <div className="bg-purple-50 rounded-xl p-4 text-center">
                                            <div className="text-2xl font-bold text-purple-600">$47K</div>
                                            <div className="text-sm text-gray-600">Pipeline Value</div>
                                        </div>
                                        <div className="bg-orange-50 rounded-xl p-4 text-center">
                                            <div className="text-2xl font-bold text-orange-600">23%</div>
                                            <div className="text-sm text-gray-600">Conversion Rate</div>
                                        </div>
                                    </div>

                                    <div className="bg-gray-50 rounded-xl p-4">
                                        <div className="flex justify-between items-center mb-2">
                                            <span className="text-sm font-medium text-gray-700">AI Optimization</span>
                                            <span className="text-sm text-green-600">+24% this week</span>
                                        </div>
                                        <div className="w-full bg-gray-200 rounded-full h-2">
                                            <div className="bg-blue-500 h-2 rounded-full" style={{width: '78%'}}></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            );
        };

        // Technology Stack Component
        const Technology = () => {
            const techRef = useRef(null);
            useEffect(() => {
                gsap.from(techRef.current.children, {
                    opacity: 0,
                    y: 40,
                    stagger: 0.1,
                    duration: 0.8,
                    ease: 'power3.out',
                    scrollTrigger: { trigger: techRef.current, start: 'top 80%' }
                });
            }, []);

            const techStack = [
                { name: 'GPT-4 & Claude', category: 'AI Models', icon: '🧠' },
                { name: 'TensorFlow', category: 'Machine Learning', icon: '⚡' },
                { name: 'Python & Node.js', category: 'Backend', icon: '🔧' },
                { name: 'React & Next.js', category: 'Frontend', icon: '⚛️' },
                { name: 'AWS & Azure', category: 'Cloud Infrastructure', icon: '☁️' },
                { name: 'PostgreSQL', category: 'Database', icon: '🗄️' }
            ];

            return (
                <section id="technology" className="py-24 bg-white">
                    <div className="container mx-auto px-6">
                        <div className="text-center mb-16">
                            <div className="inline-block bg-blue-50 text-blue-600 px-4 py-2 rounded-full text-sm font-medium mb-4">
                                🔬 Technology Stack
                            </div>
                            <h2 className="text-4xl lg:text-5xl font-bold mb-6 text-gray-900">
                                Built on <span className="text-blue-500">Cutting-Edge</span> Technology
                            </h2>
                            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                                Our AI automation platform is powered by the latest advances in artificial intelligence,
                                machine learning, and cloud computing.
                            </p>
                        </div>

                        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6" ref={techRef}>
                            {techStack.map((tech, index) => (
                                <div key={index} className="hover-card bg-white p-6 rounded-xl shadow-lg text-center">
                                    <div className="text-4xl mb-4">{tech.icon}</div>
                                    <h3 className="text-xl font-bold mb-2 text-gray-900">{tech.name}</h3>
                                    <p className="text-gray-600">{tech.category}</p>
                                </div>
                            ))}
                        </div>

                        <div className="mt-16 bg-gradient-to-r from-blue-500 to-blue-600 rounded-3xl p-8 text-white text-center">
                            <h3 className="text-2xl font-bold mb-4">Enterprise-Grade Security & Reliability</h3>
                            <div className="grid md:grid-cols-3 gap-8">
                                <div>
                                    <div className="text-3xl font-bold mb-2">99.9%</div>
                                    <div className="text-blue-100">Uptime Guarantee</div>
                                </div>
                                <div>
                                    <div className="text-3xl font-bold mb-2">SOC 2</div>
                                    <div className="text-blue-100">Compliance</div>
                                </div>
                                <div>
                                    <div className="text-3xl font-bold mb-2">24/7</div>
                                    <div className="text-blue-100">Support</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            );
        };

        // Results Component
        const Results = () => {
            const resultsRef = useRef(null);
            const [counters, setCounters] = useState([0, 0, 0, 0]);

            useEffect(() => {
                gsap.from(resultsRef.current.children, {
                    opacity: 0,
                    y: 50,
                    stagger: 0.2,
                    duration: 1,
                    ease: 'power3.out',
                    scrollTrigger: {
                        trigger: resultsRef.current,
                        start: 'top 80%',
                        onEnter: () => {
                            const targets = [50000, 98, 500, 24];
                            targets.forEach((target, index) => {
                                gsap.to({}, {
                                    duration: 2,
                                    ease: 'power2.out',
                                    onUpdate: function() {
                                        const progress = this.progress();
                                        setCounters(prev => {
                                            const newCounters = [...prev];
                                            newCounters[index] = Math.floor(target * progress);
                                            return newCounters;
                                        });
                                    }
                                });
                            });
                        }
                    }
                });
            }, []);

            const metrics = [
                { value: counters[0], suffix: '+', label: 'Leads Generated', desc: 'High-quality leads delivered to our clients' },
                { value: counters[1], suffix: '%', label: 'Automation Success', desc: 'Tasks automated successfully without errors' },
                { value: counters[2], suffix: '+', label: 'Active Automations', desc: 'AI workflows running across all clients' },
                { value: 24, suffix: '/7', label: 'Support Available', desc: 'Round-the-clock expert assistance' }
            ];

            return (
                <section id="results" className="py-24 bg-gradient-to-br from-blue-50 to-indigo-50">
                    <div className="container mx-auto px-6">
                        <div className="text-center mb-16">
                            <div className="inline-block bg-blue-100 text-blue-600 px-4 py-2 rounded-full text-sm font-medium mb-4">
                                📊 Proven Results
                            </div>
                            <h2 className="text-4xl lg:text-5xl font-bold mb-6 text-gray-900">
                                Real Numbers, <span className="text-blue-500">Real Impact</span>
                            </h2>
                            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                                See the measurable results our AI automation and lead generation solutions deliver for businesses like yours.
                            </p>
                        </div>

                        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16" ref={resultsRef}>
                            {metrics.map((metric, index) => (
                                <div key={index} className="bg-white rounded-2xl p-8 text-center hover-card shadow-lg">
                                    <div className="text-4xl lg:text-5xl font-bold text-blue-500 mb-4">
                                        {metric.value.toLocaleString()}{metric.suffix}
                                    </div>
                                    <h3 className="text-xl font-semibold text-gray-900 mb-2">{metric.label}</h3>
                                    <p className="text-gray-600 text-sm">{metric.desc}</p>
                                </div>
                            ))}
                        </div>

                        <div className="bg-white rounded-3xl p-8 shadow-xl">
                            <div className="text-center mb-8">
                                <h3 className="text-2xl font-bold text-gray-900 mb-4">Client Success Stories</h3>
                                <p className="text-gray-600">Real testimonials from businesses we've helped grow</p>
                            </div>

                            <div className="grid md:grid-cols-3 gap-8">
                                <div className="text-center">
                                    <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                        <span className="text-2xl">🚀</span>
                                    </div>
                                    <blockquote className="text-gray-600 italic mb-4">
                                        "LeadBaseAI increased our lead generation by 300% in just 2 months. The AI automation is incredible!"
                                    </blockquote>
                                    <div className="font-semibold text-gray-900">Sarah Johnson</div>
                                    <div className="text-sm text-gray-500">CEO, TechStart Inc.</div>
                                </div>

                                <div className="text-center">
                                    <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                        <span className="text-2xl">⚡</span>
                                    </div>
                                    <blockquote className="text-gray-600 italic mb-4">
                                        "The automation saved us 40 hours per week. Our team can now focus on high-value activities."
                                    </blockquote>
                                    <div className="font-semibold text-gray-900">Mike Chen</div>
                                    <div className="text-sm text-gray-500">Operations Director, ScaleUp Co.</div>
                                </div>

                                <div className="text-center">
                                    <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                        <span className="text-2xl">💰</span>
                                    </div>
                                    <blockquote className="text-gray-600 italic mb-4">
                                        "ROI was immediate. We saw a 250% increase in qualified leads within the first month."
                                    </blockquote>
                                    <div className="font-semibold text-gray-900">Lisa Rodriguez</div>
                                    <div className="text-sm text-gray-500">Marketing Manager, GrowthCorp</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            );
        };

        // Contact Component
        const Contact = () => {
            const contactRef = useRef(null);
            useEffect(() => {
                gsap.from(contactRef.current.children, {
                    opacity: 0,
                    y: 50,
                    stagger: 0.2,
                    duration: 1,
                    ease: 'power3.out',
                    scrollTrigger: { trigger: contactRef.current, start: 'top 80%' }
                });
            }, []);

            return (
                <section id="contact" className="py-24 bg-white">
                    <div className="container mx-auto px-6">
                        <div className="max-w-4xl mx-auto">
                            <div className="text-center mb-16" ref={contactRef}>
                                <div className="inline-block bg-blue-50 text-blue-600 px-4 py-2 rounded-full text-sm font-medium mb-4">
                                    🚀 Ready to Get Started?
                                </div>
                                <h2 className="text-4xl lg:text-5xl font-bold mb-6 text-gray-900">
                                    Transform Your Business with <span className="text-blue-500">AI Automation</span>
                                </h2>
                                <p className="text-xl text-gray-600 max-w-2xl mx-auto">
                                    Join hundreds of businesses already using LeadBaseAI to automate their workflows and generate more qualified leads.
                                </p>
                            </div>

                            <div className="grid lg:grid-cols-2 gap-12 items-center">
                                <div className="space-y-8">
                                    <div className="bg-blue-50 rounded-2xl p-8">
                                        <h3 className="text-2xl font-bold text-gray-900 mb-6">What You Get:</h3>
                                        <div className="space-y-4">
                                            <div className="flex items-center space-x-3">
                                                <svg className="w-5 h-5 text-blue-500" fill="currentColor" viewBox="0 0 24 24">
                                                    <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z" />
                                                </svg>
                                                <span className="text-gray-700">Free consultation & strategy session</span>
                                            </div>
                                            <div className="flex items-center space-x-3">
                                                <svg className="w-5 h-5 text-blue-500" fill="currentColor" viewBox="0 0 24 24">
                                                    <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z" />
                                                </svg>
                                                <span className="text-gray-700">Custom AI automation setup</span>
                                            </div>
                                            <div className="flex items-center space-x-3">
                                                <svg className="w-5 h-5 text-blue-500" fill="currentColor" viewBox="0 0 24 24">
                                                    <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z" />
                                                </svg>
                                                <span className="text-gray-700">Lead generation system implementation</span>
                                            </div>
                                            <div className="flex items-center space-x-3">
                                                <svg className="w-5 h-5 text-blue-500" fill="currentColor" viewBox="0 0 24 24">
                                                    <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z" />
                                                </svg>
                                                <span className="text-gray-700">Ongoing optimization & support</span>
                                            </div>
                                        </div>
                                    </div>

                                    <div className="space-y-4">
                                        <button className="w-full btn-primary text-lg py-4">
                                            Schedule Free Consultation
                                        </button>
                                        <button className="w-full btn-outline text-lg py-4">
                                            Download Case Studies
                                        </button>
                                    </div>
                                </div>

                                <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-3xl p-8">
                                    <h3 className="text-2xl font-bold text-gray-900 mb-6">Get Started Today</h3>
                                    <div className="space-y-6">
                                        <input
                                            type="text"
                                            placeholder="Your Name"
                                            className="w-full bg-white border border-gray-200 rounded-lg px-4 py-3 text-gray-900 placeholder-gray-500 focus:border-blue-500 focus:outline-none"
                                        />
                                        <input
                                            type="email"
                                            placeholder="Business Email"
                                            className="w-full bg-white border border-gray-200 rounded-lg px-4 py-3 text-gray-900 placeholder-gray-500 focus:border-blue-500 focus:outline-none"
                                        />
                                        <input
                                            type="text"
                                            placeholder="Company Name"
                                            className="w-full bg-white border border-gray-200 rounded-lg px-4 py-3 text-gray-900 placeholder-gray-500 focus:border-blue-500 focus:outline-none"
                                        />
                                        <select className="w-full bg-white border border-gray-200 rounded-lg px-4 py-3 text-gray-900 focus:border-blue-500 focus:outline-none">
                                            <option>What's your main goal?</option>
                                            <option>Automate workflows</option>
                                            <option>Generate more leads</option>
                                            <option>Improve efficiency</option>
                                            <option>Scale operations</option>
                                        </select>
                                        <textarea
                                            placeholder="Tell us about your current challenges..."
                                            rows="4"
                                            className="w-full bg-white border border-gray-200 rounded-lg px-4 py-3 text-gray-900 placeholder-gray-500 focus:border-blue-500 focus:outline-none resize-none"
                                        ></textarea>
                                        <button className="w-full btn-primary text-lg py-4">
                                            Send Message
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            );
        };

        // Footer Component
        const Footer = () => (
            <footer className="py-16 bg-gray-900 border-t border-blue-200">
                <div className="container mx-auto px-6">
                    <div className="grid md:grid-cols-4 gap-8 mb-12">
                        <div className="space-y-4">
                            <div className="flex items-center space-x-4">
                                <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl flex items-center justify-center">
                                    <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M13 3L4 14h7v7l9-11h-7V3z" />
                                    </svg>
                                </div>
                                <div>
                                    <div className="text-xl font-bold text-white">LeadBaseAI</div>
                                    <div className="text-xs text-blue-400">AI AUTOMATION & LEADS</div>
                                </div>
                            </div>
                            <p className="text-gray-400 text-sm">
                                Transforming businesses with intelligent AI automation and lead generation solutions.
                            </p>
                        </div>

                        <div>
                            <h4 className="text-white font-bold mb-4">Solutions</h4>
                            <div className="space-y-2 text-sm">
                                <a href="#" className="block text-gray-400 hover:text-blue-400 transition-colors">AI Automation</a>
                                <a href="#" className="block text-gray-400 hover:text-blue-400 transition-colors">Lead Generation</a>
                                <a href="#" className="block text-gray-400 hover:text-blue-400 transition-colors">CRM Integration</a>
                                <a href="#" className="block text-gray-400 hover:text-blue-400 transition-colors">Analytics</a>
                            </div>
                        </div>

                        <div>
                            <h4 className="text-white font-bold mb-4">Resources</h4>
                            <div className="space-y-2 text-sm">
                                <a href="#" className="block text-gray-400 hover:text-blue-400 transition-colors">Documentation</a>
                                <a href="#" className="block text-gray-400 hover:text-blue-400 transition-colors">Case Studies</a>
                                <a href="#" className="block text-gray-400 hover:text-blue-400 transition-colors">Blog</a>
                                <a href="#" className="block text-gray-400 hover:text-blue-400 transition-colors">Support</a>
                            </div>
                        </div>

                        <div>
                            <h4 className="text-white font-bold mb-4">Connect</h4>
                            <div className="space-y-2 text-sm">
                                <a href="#" className="block text-gray-400 hover:text-blue-400 transition-colors">LinkedIn</a>
                                <a href="#" className="block text-gray-400 hover:text-blue-400 transition-colors">Twitter</a>
                                <a href="#" className="block text-gray-400 hover:text-blue-400 transition-colors">Email</a>
                                <a href="#" className="block text-gray-400 hover:text-blue-400 transition-colors">Schedule Call</a>
                            </div>
                        </div>
                    </div>

                    <div className="border-t border-gray-700 pt-8">
                        <div className="flex flex-col md:flex-row justify-between items-center">
                            <p className="text-gray-400 text-sm">
                                © 2025 LeadBaseAI. All rights reserved.
                            </p>
                            <div className="flex space-x-6 mt-4 md:mt-0">
                                <a href="#" className="text-gray-400 hover:text-blue-400 transition-colors text-sm">Privacy Policy</a>
                                <a href="#" className="text-gray-400 hover:text-blue-400 transition-colors text-sm">Terms of Service</a>
                                <a href="#" className="text-gray-400 hover:text-blue-400 transition-colors text-sm">Security</a>
                            </div>
                        </div>
                    </div>
                </div>
            </footer>
        );

        // Main App Component
        const App = () => {
            return (
                <div className="light-gradient">
                    <Nav />
                    <Hero />
                    <Automation />
                    <LeadGeneration />
                    <Technology />
                    <Results />
                    <Contact />
                    <Footer />
                </div>
            );
        };

        ReactDOM.render(<App />, document.getElementById('root'));
    </script>
</body>
</html>
