<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GameNeXT - AI Automation & Lead Generation</title>
    <script src="https://cdn.jsdelivr.net/npm/react@18.2.0/umd/react.production.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/react-dom@18.2.0/umd/react-dom.production.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@babel/standalone@7.22.5/babel.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/gsap@3.12.2/dist/gsap.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/gsap@3.12.2/dist/ScrollTrigger.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&family=Montserrat:wght@700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; transition: background-color 0.3s ease, color 0.3s ease; }
        h1, h2, h3, h4, h5, h6 { font-family: 'Montserrat', sans-serif; }
        .glow-button:hover {
            box-shadow: 0 0 25px rgba(100, 181, 246, 0.8); /* Enhanced Light Blue glow */
            transform: translateY(-3px); /* Subtle lift effect */
            transition: all 0.3s ease;
        }
        .nav-link {
            position: relative;
            font-weight: 600;
            color: #4A5568; /* Darker grey for better contrast on light nav */
        }
        .nav-link:hover {
            color: #2B6CB0; /* Darker blue on hover */
        }
        .nav-link:hover::after {
            content: '';
            position: absolute;
            bottom: -6px; /* Adjusted for better visual separation */
            left: 0;
            width: 100%;
            height: 3px; /* Thicker underline */
            background: #64B5F6; /* Light Blue */
            transform: scaleX(1);
            transform-origin: bottom left; /* Changed origin for a different animation feel */
            transition: transform 0.3s ease;
        }
        .nav-link::after {
            content: '';
            position: absolute;
            bottom: -6px;
            left: 0;
            width: 100%;
            height: 3px;
            background: #64B5F6; /* Light Blue */
            transform: scaleX(0);
            transform-origin: bottom left;
            transition: transform 0.3s ease;
        }
        .gradient-bg-dark {
            background: linear-gradient(180deg, #F8F9FA 0%, #E9ECEF 100%); /* Lighter greyish white */
            color: #212529; /* Dark text for light background */
        }
        .gradient-bg-light {
            background: linear-gradient(180deg, #F8F9FA 0%, #E9ECEF 100%); /* Lighter greyish white */
            color: #212529; /* Dark text for light background */
        }
        .particle-bg {
            position: absolute;
            inset: 0;
            pointer-events: none;
            opacity: 0.6; /* Slightly less opaque particles */
        }
        .hamburger div {
            transition: all 0.3s ease;
        }
        .hamburger.open div:nth-child(1) {
            transform: rotate(45deg) translate(5px, 5px);
        }
        .hamburger.open div:nth-child(2) {
            opacity: 0;
        }
        .hamburger.open div:nth-child(3) {
            transform: rotate(-45deg) translate(7px, -7px);
        }
        @keyframes slide {
            0% { transform: translateX(0); }
            100% { transform: translateX(-50%); }
        }
    </style>
</head>
<body className="gradient-bg-dark">
    <div id="root"></div>
    <script type="text/babel">
        const { useEffect, useRef, useState } = React;

        // Nav Component with Hamburger Menu
        const Nav = ({ isDark, toggleTheme }) => {
            const [isOpen, setIsOpen] = useState(false);
            const navRef = useRef(null);
            const menuRef = useRef(null);

            useEffect(() => {
                gsap.from(navRef.current.children, {
                    y: -20,
                    opacity: 0,
                    stagger: 0.1,
                    duration: 0.5,
                    ease: 'power3.out'
                });
                if (isOpen) {
                    gsap.from(menuRef.current.children, {
                        x: 100,
                        opacity: 0,
                        stagger: 0.1,
                        duration: 0.5,
                        ease: 'power3.out',
                        delay: 0.5
                    });
                }
            }, [isOpen]);

            return (
                <nav className="fixed top-0 w-full bg-white/90 backdrop-blur-md z-50 py-4 shadow-md" ref={navRef}>
                    <div className="container mx-auto px-4 flex justify-between items-center">
                        <div className="flex items-center">
                            <div className="w-10 h-10 bg-[#64B5F6] rounded-full flex items-center justify-center mr-4 relative overflow-hidden shadow-lg">
                                <svg className="w-6 h-6 text-white animate-pulse" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M12 2L3 12h6v10l6-10h-6z" />
                                </svg>
                            </div>
                            <span className="text-2xl font-bold text-[#212529]">GameNeXT</span>
                        </div>
                        <div className="md:hidden">
                            <button onClick={() => setIsOpen(!isOpen)} className="hamburger flex flex-col space-y-1.5">
                                <div className="w-8 h-1 bg-[#64B5F6] rounded"></div>
                                <div className="w-8 h-1 bg-[#64B5F6] rounded"></div>
                                <div className="w-8 h-1 bg-[#64B5F6] rounded"></div>
                            </button>
                        </div>
                        <div className={`md:flex md:space-x-8 ${isOpen ? 'flex' : 'hidden'} flex-col md:flex-row absolute md:static top-16 left-0 w-full md:w-auto bg-white/95 md:bg-transparent p-4 md:p-0 shadow-lg md:shadow-none`} ref={menuRef}>
                            <a href="#how-it-works" className="nav-link py-2 md:py-0" onClick={() => setIsOpen(false)}>How It Works</a>
                            <a href="#why-us" className="nav-link py-2 md:py-0" onClick={() => setIsOpen(false)}>Why Us</a>
                            <a href="#tech" className="nav-link py-2 md:py-0" onClick={() => setIsOpen(false)}>Technology</a>
                            <a href="#results" className="nav-link py-2 md:py-0" onClick={() => setIsOpen(false)}>Results</a>
                            <a href="#success-stories" className="nav-link py-2 md:py-0" onClick={() => setIsOpen(false)}>Success Stories</a>
                            <a href="#use-cases" className="nav-link py-2 md:py-0" onClick={() => setIsOpen(false)}>Use Cases</a>
                            <a href="#cta" className="nav-link py-2 md:py-0" onClick={() => setIsOpen(false)}>Contact</a>
                            <button onClick={toggleTheme} className="flex items-center space-x-2 mt-4 md:mt-0 text-[#4A5568] hover:text-[#2B6CB0]">
                                <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                    {isDark ? (
                                        <path d="M12 2a10 10 0 100 20 10 10 0 000-20zm0 18a8 8 0 110-16 8 8 0 010 16zm0-12v4l3 2-1 2-4-2V8h2z" />
                                    ) : (
                                        <path d="M12 2a10 10 0 00-10 10c0 5.52 4.48 10 10 10s10-4.48 10-10S17.52 2 12 2zm0 18a8 8 0 01-8-8c0-4.42 3.58-8 8-8s8 3.58 8 8-3.58 8-8 8zm0-14a6 6 0 00-6 6c0 3.31 2.69 6 6 6s6-2.69 6-6-2.69-6-6-6z" />
                                    )}
                                </svg>
                                <span>{isDark ? 'Light Mode' : 'Dark Mode'}</span>
                            </button>
                        </div>
                    </div>
                </nav>
            );
        };

        // Hero Component
        const Hero = () => {
            const heroRef = useRef(null);
            useEffect(() => {
                gsap.from(heroRef.current.children, {
                    opacity: 0,
                    y: 50,
                    stagger: 0.2,
                    duration: 1,
                    ease: 'power3.out'
                });
                const particles = document.createElement('div');
                particles.className = 'particle-bg';
                heroRef.current.appendChild(particles);
                for (let i = 0; i < 30; i++) {
                    const particle = document.createElement('div');
                    particle.className = 'absolute bg-[#64B5F6]/30 rounded-full'; /* Light Blue particles */
                    particle.style.width = `${Math.random() * 12 + 5}px`;
                    particle.style.height = particle.style.width;
                    particles.appendChild(particle);
                    gsap.to(particle, {
                        x: Math.random() * window.innerWidth,
                        y: Math.random() * window.innerHeight,
                        opacity: Math.random() * 0.5,
                        duration: 5 + Math.random() * 5,
                        repeat: -1,
                        yoyo: true,
                        ease: 'sine.inOut'
                    });
                }
            }, []);
            return (
                <section className="min-h-screen flex items-center relative overflow-hidden py-20" ref={heroRef}>
                    <div className="absolute inset-0 bg-[url('https://source.unsplash.com/random/1920x1080?abstract-tech')] opacity-15 bg-cover bg-center"></div>
                    <div className="container mx-auto px-4 relative z-10 text-center md:text-left">
                        <div className="max-w-3xl mx-auto md:mx-0">
                            <h1 className="text-5xl md:text-7xl font-extrabold mb-6 leading-tight text-[#212529] drop-shadow-lg">
                                Scale on Autopilot with <span className="text-[#64B5F6]">AI-Powered Systems</span>
                            </h1>
                            <p className="text-xl md:text-2xl mb-10 text-[#495057] leading-relaxed">
                                Done-for-you AI workflows, smart outreach systems, and lightning-fast automation built to grow your business. Experience seamless growth with GameNeXT's cutting-edge solutions.
                            </p>
                            <div className="flex flex-col sm:flex-row justify-center md:justify-start space-y-4 sm:space-y-0 sm:space-x-6">
                                <a href="#cta" className="bg-[#64B5F6] text-white px-10 py-4 rounded-full text-lg font-semibold glow-button flex items-center justify-center shadow-lg hover:shadow-xl">
                                    <svg className="w-6 h-6 mr-3 animate-bounce" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M12 2L3 12h6v10l6-10h-6z" />
                                    </svg>
                                    Book a Free Call
                                </a>
                                <a href="#demo" className="border border-[#64B5F6] text-[#64B5F6] px-10 py-4 rounded-full text-lg font-semibold hover:bg-[#64B5F6] hover:text-white glow-button flex items-center justify-center shadow-lg hover:shadow-xl">
                                    Watch Demo
                                </a>
                            </div>
                        </div>
                    </div>
                </section>
            );
        };

        // How It Works Component
        const HowItWorks = () => {
            const stepsRef = useRef(null);
            useEffect(() => {
                gsap.from(stepsRef.current.children, {
                    opacity: 0,
                    x: -100,
                    stagger: 0.3,
                    duration: 1,
                    ease: 'power3.out',
                    scrollTrigger: { trigger: stepsRef.current, start: 'top 80%' }
                });
            }, []);
            return (
                <section id="how-it-works" className="py-32">
                    <div className="container mx-auto px-4">
                        <h2 className="text-4xl md:text-5xl font-extrabold text-center mb-16 text-[#212529]">How GameNeXT Supercharges Your Growth</h2>
                        <p className="text-lg md:text-xl text-center mb-12 max-w-4xl mx-auto text-[#495057]">Our process is designed to integrate seamlessly with your business, delivering measurable results through AI-driven automation and precision targeting.</p>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-10" ref={stepsRef}>
                            {[
                                { title: 'AI Automation Workflows', desc: 'Our custom AI workflows automate repetitive tasks, streamline operations, and integrate with your existing tools, saving you time and resources.', icon: '⚙️' },
                                { title: 'Hyper-Personalized Lead Generation', desc: 'Using advanced AI, we craft targeted outreach campaigns that resonate with your audience, driving higher conversion rates.', icon: '🎯' },
                                { title: 'Auto-Scaling Campaign Engines', desc: 'Our scalable campaign engines adapt to your growth, ensuring consistent performance as your business expands.', icon: '🚀' }
                            ].map((step, index) => (
                                <div key={index} className="relative p-8 bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 group border border-gray-200">
                                    <div className="absolute inset-0 bg-[#64B5F6]/10 group-hover:bg-[#64B5F6]/20 transition-colors duration-300 rounded-xl"></div>
                                    <span className="text-5xl mb-6 block text-center relative z-10">{step.icon}</span>
                                    <h3 className="text-2xl font-bold mb-4 text-[#212529] text-center relative z-10">{step.title}</h3>
                                    <p className="relative z-10 text-[#495057] text-center">{step.desc}</p>
                                </div>
                            ))}
                        </div>
                        <div className="text-center mt-16">
                            <a href="#cta" className="bg-[#64B5F6] text-white px-10 py-4 rounded-full text-lg font-semibold glow-button inline-flex items-center shadow-lg hover:shadow-xl">
                                <svg className="w-6 h-6 mr-3 animate-bounce" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M12 2L3 12h6v10l6-10h-6z" />
                                </svg>
                                Get Started Now
                            </a>
                        </div>
                    </div>
                </section>
            );
        };

        // Our Technology Component
        const Technology = () => {
            const techRef = useRef(null);
            useEffect(() => {
                gsap.from(techRef.current.children, {
                    opacity: 0,
                    y: 50,
                    stagger: 0.2,
                    duration: 1,
                    ease: 'power3.out',
                    scrollTrigger: { trigger: techRef.current, start: 'top 80%' }
                });
            }, []);
            return (
                <section id="tech" className="py-32 bg-gray-50">
                    <div className="container mx-auto px-4">
                        <h2 className="text-4xl md:text-5xl font-extrabold text-center mb-16 text-[#212529]">Our Cutting-Edge Technology Stack</h2>
                        <p className="text-lg md:text-xl text-center mb-12 max-w-4xl mx-auto text-[#495057]">GameNeXT leverages state-of-the-art AI and machine learning technologies to deliver unparalleled automation and lead generation solutions.</p>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-10" ref={techRef}>
                            {[
                                { title: 'AI-Powered Analytics', desc: 'Real-time insights powered by advanced AI algorithms, enabling data-driven decisions that optimize your campaigns.' },
                                { title: 'Machine Learning Models', desc: 'Our adaptive models learn from your data, continuously improving performance and personalization.' },
                                { title: 'Cloud-Native Integration', desc: 'Seamless connectivity with your existing tools like CRMs, email platforms, and more, all hosted on secure cloud infrastructure.' },
                                { title: 'No-Code Automation', desc: 'Build complex workflows without writing a single line of code, making automation accessible to everyone.' },
                                { title: 'Scalable Infrastructure', desc: 'Our systems scale effortlessly, handling thousands of leads and processes without compromising speed or accuracy.' }
                            ].map((tech, index) => (
                                <div key={index} className="p-8 bg-white rounded-xl shadow-lg hover:shadow-xl transition-transform duration-300 transform hover:-translate-y-2 text-center border border-gray-200">
                                    <svg className="w-12 h-12 mx-auto mb-6 text-[#64B5F6] animate-pulse" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M12 2a10 10 0 100 20 10 10 0 000-20zm0 18a8 8 0 110-16 8 8 0 010 16zm0-12v4l3 2-1 2-4-2V8h2z" />
                                    </svg>
                                    <h3 className="text-2xl font-bold mb-4 text-[#212529]">{tech.title}</h3>
                                    <p className="text-[#495057]">{tech.desc}</p>
                                </div>
                            ))}
                        </div>
                        <div className="text-center mt-16">
                            <a href="#cta" className="bg-[#64B5F6] text-white px-10 py-4 rounded-full text-lg font-semibold glow-button inline-flex items-center shadow-lg hover:shadow-xl">
                                <svg className="w-6 h-6 mr-3 animate-bounce" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M12 2L3 12h6v10l6-10h-6z" />
                                </svg>
                                Explore Our Tech
                            </a>
                        </div>
                    </div>
                </section>
            );
        };

        // Why Choose GameNeXT Component
        const WhyChoose = () => {
            const compareRef = useRef(null);
            useEffect(() => {
                gsap.from(compareRef.current.children, {
                    opacity: 0,
                    x: (index) => index % 2 === 0 ? -100 : 100,
                    stagger: 0.3,
                    duration: 1,
                    ease: 'power3.out',
                    scrollTrigger: { trigger: compareRef.current, start: 'top 80%' }
                });
            }, []);
            return (
                <section id="why-us" className="py-32">
                    <div className="container mx-auto px-4">
                        <h2 className="text-4xl md:text-5xl font-extrabold text-center mb-16 text-[#212529]">Why GameNeXT Outshines the Competition</h2>
                        <p className="text-lg md:text-xl text-center mb-12 max-w-4xl mx-auto text-[#495057]">Unlike traditional agencies, GameNeXT combines AI innovation with speed and scalability to deliver results that transform businesses.</p>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-10 mb-16" ref={compareRef}>
                            <div className="p-8 bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-200">
                                <h3 className="text-3xl font-bold mb-6 text-red-600">Manual Agencies</h3>
                                <ul className="space-y-4 text-lg text-[#495057]">
                                    <li className="flex items-center"><span className="mr-3 text-red-500">⛔</span> Slow setup (weeks)</li>
                                    <li className="flex items-center"><span className="mr-3 text-red-500">⛔</span> Limited scalability</li>
                                    <li className="flex items-center"><span className="mr-3 text-red-500">⛔</span> Generic campaigns</li>
                                    <li className="flex items-center"><span className="mr-3 text-red-500">⛔</span> Manual processes</li>
                                    <li className="flex items-center"><span className="mr-3 text-red-500">⛔</span> High costs for customization</li>
                                </ul>
                            </div>
                            <div className="p-8 bg-[#64B5F6] text-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
                                <h3 className="text-3xl font-bold mb-6">GameNeXT</h3>
                                <ul className="space-y-4 text-lg">
                                    <li className="flex items-center"><span className="mr-3 text-green-300">✅</span> Instant setup (hours)</li>
                                    <li className="flex items-center"><span className="mr-3 text-green-300">✅</span> Infinite scalability</li>
                                    <li className="flex items-center"><span className="mr-3 text-green-300">✅</span> Hyper-personalized</li>
                                    <li className="flex items-center"><span className="mr-3 text-green-300">✅</span> Fully automated</li>
                                    <li className="flex items-center"><span className="mr-3 text-green-300">✅</span> Cost-effective solutions</li>
                                </ul>
                            </div>
                        </div>
                        <div className="flex flex-wrap justify-center gap-6 mt-16">
                            {['Powered by GPT-4o', 'No-Code Friendly', '24/7 Support', '99.9% Uptime'].map((badge, index) => (
                                <span key={index} className="bg-[#64B5F6] text-white px-8 py-3 rounded-full text-md font-semibold glow-button shadow-md animate-pulse">{badge}</span>
                            ))}
                        </div>
                    </div>
                </section>
            );
        };

        // Results Component
        const Results = () => {
            const countersRef = useRef(null);
            useEffect(() => {
                gsap.from(countersRef.current.children, {
                    opacity: 0,
                    y: 50,
                    stagger: 0.3,
                    duration: 1.2,
                    ease: 'power3.out',
                    scrollTrigger: { trigger: countersRef.current, start: 'top 80%' }
                });
            }, []);
            return (
                <section id="results" className="py-32 bg-gray-50">
                    <div className="container mx-auto px-4">
                        <h2 className="text-4xl md:text-5xl font-extrabold text-center mb-16 text-[#212529]">Proven Results & Client Wins</h2>
                        <p className="text-lg md:text-xl text-center mb-12 max-w-4xl mx-auto text-[#495057]">Our clients have achieved remarkable growth with GameNeXT's AI-powered solutions. See the numbers that prove our impact.</p>
                        <div className="grid grid-cols-1 md:grid-cols-4 gap-10 mb-16" ref={countersRef}>
                            {[
                                { value: '10,000+', label: 'Leads Delivered', desc: 'High-quality leads generated for businesses worldwide.' },
                                { value: '50+', label: 'AI Systems Deployed', desc: 'Custom automation systems powering business growth.' },
                                { value: '98%', label: 'Automation Accuracy', desc: 'Near-perfect precision in automated processes.' },
                                { value: '200+', label: 'Campaigns Launched', desc: 'Successful campaigns driving measurable results.' }
                            ].map((stat, index) => (
                                <div key={index} className="text-center p-8 bg-white rounded-xl shadow-lg hover:shadow-xl transition-transform duration-300 transform hover:-translate-y-2 border border-gray-200">
                                    <h3 className="text-4xl font-bold text-[#64B5F6] mb-2 animate-pulse">{stat.value}</h3>
                                    <p className="text-lg font-semibold text-[#212529]">{stat.label}</p>
                                    <p className="text-sm mt-2 text-[#495057]">{stat.desc}</p>
                                </div>
                            ))}
                        </div>
                        <div className="text-center">
                            <a href="#cta" className="bg-[#64B5F6] text-white px-10 py-4 rounded-full text-lg font-semibold glow-button inline-flex items-center shadow-lg hover:shadow-xl">
                                <svg className="w-6 h-6 mr-3 animate-bounce" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M12 2L3 12h6v10l6-10h-6z" />
                                </svg>
                                See Your Potential
                            </a>
                        </div>
                    </div>
                </section>
            );
        };

        // Success Stories Component
        const SuccessStories = () => {
            const sliderRef = useRef(null);
            useEffect(() => {
                gsap.from(sliderRef.current.children, {
                    opacity: 0,
                    x: 100,
                    stagger: 0.3,
                    duration: 1,
                    ease: 'power3.out',
                    scrollTrigger: { trigger: sliderRef.current, start: 'top 80%' }
                });
            }, []);
            return (
                <section id="success-stories" className="py-32">
                    <div className="container mx-auto px-4">
                        <h2 className="text-5xl font-bold text-center mb-16 text-[#1A1A1A]">Client Success Stories</h2>
                        <p className="text-lg text-center mb-12 max-w-3xl mx-auto text-[#1A1A1A]">Hear from our clients who have transformed their businesses with GameNeXT's AI automation and lead generation solutions.</p>
                        <div className="relative overflow-hidden" ref={sliderRef}>
                            <div className="flex space-x-8 animate-[slide_20s_linear_infinite]">
                                {[
                                    { quote: 'GameNeXT transformed our SaaS launch with unparalleled automation, driving 3x more sign-ups than expected.', author: 'John D., SaaS Founder', company: 'TechTrend Innovations' },
                                    { quote: 'Our lead generation doubled in weeks with their AI systems, and the accuracy is phenomenal.', author: 'Sarah K., Marketing Lead', company: 'GrowEasy Solutions' },
                                    { quote: 'The automation accuracy is unreal. Saved us countless hours and boosted our ROI significantly.', author: 'Mike T., Sales Agency', company: 'LeadPro Agency' },
                                    { quote: 'Best decision for scaling our cold email campaigns. GameNeXT made it effortless.', author: 'Lisa R., Creator', company: 'ContentCraft' },
                                    { quote: 'Their AI workflows streamlined our CRM, making our team 10x more efficient.', author: 'Alex P., Operations Manager', company: 'DataSync Corp' }
                                ].map((testimonial, index) => (
                                    <div key={index} className="min-w-[300px] p-8 bg-white dark:bg-white dark:text-[#1A1A1A] rounded-xl hover:scale-105 transition-transform duration-300">
                                        <p className="italic text-lg mb-4 text-[#1A1A1A]">"{testimonial.quote}"</p>
                                        <p className="font-bold text-[#1A1A1A]">{testimonial.author}</p>
                                        <p className="text-sm text-gray-400 dark:text-gray-600">{testimonial.company}</p>
                                    </div>
                                ))}
                            </div>
                        </div>
                        <div className="text-center mt-12">
                            <a href="#cta" className="bg-[#64B5F6] text-white px-8 py-4 rounded-full glow-button inline-flex items-center">
                                <svg className="w-6 h-6 mr-3 animate-bounce" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M12 2L3 12h6v10l6-10h-6z" />
                                </svg>
                                Share Your Story
                            </a>
                        </div>
                    </div>
                </section>
            );
        };

        // Use Cases Component
        const UseCases = () => {
            const casesRef = useRef(null);
            useEffect(() => {
                gsap.from(casesRef.current.children, {
                    opacity: 0,
                    scale: 0.8,
                    stagger: 0.2,
                    duration: 0.8,
                    ease: 'power3.out',
                    scrollTrigger: { trigger: casesRef.current, start: 'top 80%' }
                });
            }, []);
            return (
                <section id="use-cases" className="py-32 bg-white dark:bg-white dark:text-[#1A1A1A]">
                    <div className="container mx-auto px-4">
                        <h2 className="text-5xl font-bold text-center mb-16 text-[#1A1A1A]">Tailored Use Cases for Your Business</h2>
                        <p className="text-lg text-center mb-12 max-w-3xl mx-auto text-[#1A1A1A]">GameNeXT's AI solutions are designed for a wide range of industries and applications, ensuring your business achieves maximum efficiency and growth.</p>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12" ref={casesRef}>
                            {[
                                { title: 'SaaS Launches', icon: '🚀', desc: 'Launch your SaaS product with AI-driven marketing campaigns, automated user onboarding, and real-time analytics to optimize growth.' },
                                { title: 'Cold Email Outreach', icon: '📧', desc: 'Scale your outreach with hyper-personalized email campaigns that leverage AI to target the right prospects and boost conversions.' },
                                { title: 'CRM Enrichment', icon: '📊', desc: 'Enhance your CRM with AI-powered data enrichment, ensuring your customer data is accurate, up-to-date, and actionable.' },
                                { title: 'Calendar Booking Funnels', icon: '📅', desc: 'Automate your scheduling process with intelligent booking funnels that integrate seamlessly with your calendar and CRM.' },
                                { title: 'Content Automation', icon: '✍️', desc: 'Generate high-quality content at scale with AI tools tailored for blogs, social media, and marketing materials.' },
                                { title: 'E-commerce Optimization', icon: '🛒', desc: 'Boost your e-commerce sales with AI-driven product recommendations, personalized offers, and automated customer support.' }
                            ].map((useCase, index) => (
                                <div key={index} className="p-8 bg-[#F0F8FF] dark:bg-[#F0F8FF] rounded-xl text-center hover:scale-110 hover:bg-[#64B5F6]/10 dark:hover:bg-[#1A1A1A]/10 transition-all duration-300">
                                    <span className="text-5xl mb-6 block animate-bounce">{useCase.icon}</span>
                                    <h3 className="text-2xl font-bold mb-4 text-[#1A1A1A]">{useCase.title}</h3>
                                    <p className="text-[#1A1A1A]">{useCase.desc}</p>
                                </div>
                            ))}
                        </div>
                        <div className="text-center mt-12">
                            <a href="#cta" className="bg-[#64B5F6] text-white px-8 py-4 rounded-full glow-button inline-flex items-center">
                                <svg className="w-6 h-6 mr-3 animate-bounce" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M12 2L3 12h6v10l6-10h-6z" />
                                </svg>
                                Find Your Use Case
                            </a>
                        </div>
                    </div>
                </section>
            );
        };

        // CTA Component
        const CTA = () => {
            const formRef = useRef(null);
            useEffect(() => {
                gsap.from(formRef.current.children, {
                    opacity: 0,
                    y: 50,
                    stagger: 0.2,
                    duration: 1,
                    ease: 'power3.out',
                    scrollTrigger: { trigger: formRef.current, start: 'top 80%' }
                });
            }, []);
            return (
                <section id="cta" className="py-32">
                    <div className="container mx-auto px-4 text-center">
                        <h2 className="text-5xl font-bold mb-12 animate-pulse text-[#1A1A1A]">Ready to Launch Your AI Engine?</h2>
                        <p className="text-lg mb-12 max-w-3xl mx-auto text-[#1A1A1A]">Get in touch with our team to discover how GameNeXT can transform your business with AI-powered automation and lead generation.</p>
                        <div className="max-w-md mx-auto bg-white dark:bg-white dark:text-[#1A1A1A] p-8 rounded-xl" ref={formRef}>
                            <input type="text" placeholder="Name" className="w-full mb-4 p-4 rounded bg-[#F0F8FF] dark:bg-[#F0F8FF] text-[#1A1A1A] dark:text-[#1A1A1A] border border-[#64B5F6]/30 dark:border-[#1A1A1A]/30 focus:border-[#64B5F6] dark:focus:border-[#1A1A1A] transition-colors duration-300" />
                            <input type="email" placeholder="Email" className="w-full mb-4 p-4 rounded bg-[#F0F8FF] dark:bg-[#F0F8FF] text-[#1A1A1A] dark:text-[#1A1A1A] border border-[#64B5F6]/30 dark:border-[#1A1A1A]/30 focus:border-[#64B5F6] dark:focus:border-[#1A1A1A] transition-colors duration-300" />
                            <textarea placeholder="Message" className="w-full mb-4 p-4 rounded bg-[#F0F8FF] dark:bg-[#F0F8FF] text-[#1A1A1A] dark:text-[#1A1A1A] border border-[#64B5F6]/30 dark:border-[#1A1A1A]/30 focus:border-[#64B5F6] dark:focus:border-[#1A1A1A] transition-colors duration-300" rows="5"></textarea>
                            <a href="#submit" className="bg-[#64B5F6] text-white px-8 py-4 rounded-full glow-button flex items-center justify-center mx-auto">
                                <svg className="w-6 h-6 mr-3 animate-bounce" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M12 2L3 12h6v10l6-10h-6z" />
                                </svg>
                                Submit
                            </a>
                        </div>
                    </div>
                </section>
            );
        };

        // Footer Component
        const Footer = () => (
            <footer className="py-12 bg-white dark:bg-white dark:text-[#1A1A1A] text-center">
                <p className="text-lg mb-6 text-[#1A1A1A]">© 2025 GameNeXT. All rights reserved.</p>
                <div className="flex flex-wrap justify-center gap-6">
                    <a href="#" className="text-[#64B5F6] dark:text-[#1A1A1A] hover:scale-125 transition-transform duration-300">Twitter</a>
                    <a href="#" className="text-[#64B5F6] dark:text-[#1A1A1A] hover:scale-125 transition-transform duration-300">LinkedIn</a>
                    <a href="#" className="text-[#64B5F6] dark:text-[#1A1A1A] hover:scale-125 transition-transform duration-300">Email</a>
                    <a href="#" className="text-[#64B5F6] dark:text-[#1A1A1A] hover:scale-125 transition-transform duration-300">Blog</a>
                    <a href="#" className="text-[#64B5F6] dark:text-[#1A1A1A] hover:scale-125 transition-transform duration-300">Privacy Policy</a>
                </div>
            </footer>
        );

        // Main App Component
        const App = () => {
            const [isDark, setIsDark] = useState(true);
            const toggleTheme = () => {
                setIsDark(!isDark);
                document.body.className = isDark ? 'gradient-bg-light' : 'gradient-bg-dark';
            };

            return (
                <div>
                    <Nav isDark={isDark} toggleTheme={toggleTheme} />
                    <Hero />
                    <HowItWorks />
                    <Technology />
                    <WhyChoose />
                    <Results />
                    <SuccessStories />
                    <UseCases />
                    <CTA />
                    <Footer />
                </div>
            );
        };

        ReactDOM.render(<App />, document.getElementById('root'));
    </script>
</body>
</html>
