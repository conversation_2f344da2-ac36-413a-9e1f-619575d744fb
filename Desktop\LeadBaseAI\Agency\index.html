<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NeuralFlow - Next-Gen AI Solutions</title>
    <script src="https://cdn.jsdelivr.net/npm/react@18.2.0/umd/react.production.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/react-dom@18.2.0/umd/react-dom.production.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@babel/standalone@7.22.5/babel.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/gsap@3.12.2/dist/gsap.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/gsap@3.12.2/dist/ScrollTrigger.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Space Grotesk', sans-serif;
            background: #0a0a0a;
            color: #ffffff;
            overflow-x: hidden;
        }
        .mono { font-family: 'JetBrains Mono', monospace; }

        /* Cyberpunk glow effects */
        .cyber-glow {
            box-shadow: 0 0 20px rgba(0, 255, 157, 0.3), 0 0 40px rgba(0, 255, 157, 0.1);
            border: 1px solid rgba(0, 255, 157, 0.3);
        }
        .cyber-glow:hover {
            box-shadow: 0 0 30px rgba(0, 255, 157, 0.6), 0 0 60px rgba(0, 255, 157, 0.2);
            transform: translateY(-2px);
            transition: all 0.3s ease;
        }

        /* Neon text effect */
        .neon-text {
            text-shadow: 0 0 10px rgba(0, 255, 157, 0.8), 0 0 20px rgba(0, 255, 157, 0.4);
        }

        /* Gradient backgrounds */
        .dark-gradient {
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
        }
        .purple-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .green-gradient {
            background: linear-gradient(135deg, #00ff9d 0%, #00b8d4 100%);
        }

        /* Animated grid background */
        .grid-bg {
            background-image:
                linear-gradient(rgba(0, 255, 157, 0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(0, 255, 157, 0.1) 1px, transparent 1px);
            background-size: 50px 50px;
            animation: grid-move 20s linear infinite;
        }

        @keyframes grid-move {
            0% { background-position: 0 0; }
            100% { background-position: 50px 50px; }
        }

        /* Floating particles */
        .particle {
            position: absolute;
            background: rgba(0, 255, 157, 0.6);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        /* Navigation styles */
        .nav-link {
            position: relative;
            color: #ffffff;
            transition: all 0.3s ease;
        }
        .nav-link:hover {
            color: #00ff9d;
        }
        .nav-link::after {
            content: '';
            position: absolute;
            bottom: -4px;
            left: 0;
            width: 0;
            height: 2px;
            background: linear-gradient(90deg, #00ff9d, #00b8d4);
            transition: width 0.3s ease;
        }
        .nav-link:hover::after {
            width: 100%;
        }

        /* Card hover effects */
        .hover-card {
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .hover-card:hover {
            transform: translateY(-10px) scale(1.02);
            border-color: rgba(0, 255, 157, 0.5);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        /* Glitch effect */
        .glitch {
            position: relative;
        }
        .glitch::before,
        .glitch::after {
            content: attr(data-text);
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }
        .glitch::before {
            animation: glitch-1 0.5s infinite;
            color: #ff0040;
            z-index: -1;
        }
        .glitch::after {
            animation: glitch-2 0.5s infinite;
            color: #00ff9d;
            z-index: -2;
        }

        @keyframes glitch-1 {
            0%, 14%, 15%, 49%, 50%, 99%, 100% { transform: translate(0); }
            15%, 49% { transform: translate(-2px, 2px); }
        }

        @keyframes glitch-2 {
            0%, 20%, 21%, 62%, 63%, 99%, 100% { transform: translate(0); }
            21%, 62% { transform: translate(2px, -2px); }
        }
    </style>
</head>
<body className="dark-gradient">
    <div id="root"></div>
    <script type="text/babel">
        const { useEffect, useRef, useState } = React;

        // Nav Component with Cyberpunk Design
        const Nav = () => {
            const [isOpen, setIsOpen] = useState(false);
            const navRef = useRef(null);

            useEffect(() => {
                gsap.from(navRef.current.children, {
                    y: -30,
                    opacity: 0,
                    stagger: 0.1,
                    duration: 0.8,
                    ease: 'power3.out'
                });
            }, []);

            return (
                <nav className="fixed top-0 w-full bg-black/80 backdrop-blur-xl z-50 py-4 border-b border-green-500/20" ref={navRef}>
                    <div className="container mx-auto px-6 flex justify-between items-center">
                        <div className="flex items-center space-x-4">
                            <div className="w-12 h-12 bg-gradient-to-r from-green-400 to-blue-500 rounded-lg flex items-center justify-center relative overflow-hidden cyber-glow">
                                <svg className="w-8 h-8 text-black font-bold" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M13 3L4 14h7v7l9-11h-7V3z" />
                                </svg>
                                <div className="absolute inset-0 bg-gradient-to-r from-green-400/20 to-blue-500/20 animate-pulse"></div>
                            </div>
                            <div>
                                <span className="text-2xl font-bold neon-text mono">NeuralFlow</span>
                                <div className="text-xs text-green-400 mono">AI SOLUTIONS</div>
                            </div>
                        </div>

                        <div className="hidden md:flex space-x-8">
                            <a href="#solutions" className="nav-link mono text-sm font-medium">SOLUTIONS</a>
                            <a href="#technology" className="nav-link mono text-sm font-medium">TECH STACK</a>
                            <a href="#showcase" className="nav-link mono text-sm font-medium">SHOWCASE</a>
                            <a href="#metrics" className="nav-link mono text-sm font-medium">METRICS</a>
                            <a href="#contact" className="nav-link mono text-sm font-medium">CONTACT</a>
                        </div>

                        <div className="hidden md:block">
                            <button className="cyber-glow px-6 py-2 bg-gradient-to-r from-green-400 to-blue-500 text-black font-bold rounded-lg mono text-sm">
                                DEPLOY AI
                            </button>
                        </div>

                        <div className="md:hidden">
                            <button onClick={() => setIsOpen(!isOpen)} className="text-green-400">
                                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                                </svg>
                            </button>
                        </div>
                    </div>

                    {isOpen && (
                        <div className="md:hidden bg-black/95 border-t border-green-500/20 p-4">
                            <div className="flex flex-col space-y-4">
                                <a href="#solutions" className="nav-link mono text-sm">SOLUTIONS</a>
                                <a href="#technology" className="nav-link mono text-sm">TECH STACK</a>
                                <a href="#showcase" className="nav-link mono text-sm">SHOWCASE</a>
                                <a href="#metrics" className="nav-link mono text-sm">METRICS</a>
                                <a href="#contact" className="nav-link mono text-sm">CONTACT</a>
                                <button className="cyber-glow px-6 py-2 bg-gradient-to-r from-green-400 to-blue-500 text-black font-bold rounded-lg mono text-sm w-fit">
                                    DEPLOY AI
                                </button>
                            </div>
                        </div>
                    )}
                </nav>
            );
        };

        // Hero Component with Cyberpunk Design
        const Hero = () => {
            const heroRef = useRef(null);
            const matrixRef = useRef(null);

            useEffect(() => {
                // Animate hero elements
                gsap.from(heroRef.current.children, {
                    opacity: 0,
                    y: 100,
                    stagger: 0.3,
                    duration: 1.2,
                    ease: 'power3.out'
                });

                // Create floating particles
                const createParticles = () => {
                    for (let i = 0; i < 50; i++) {
                        const particle = document.createElement('div');
                        particle.className = 'particle';
                        particle.style.width = `${Math.random() * 4 + 2}px`;
                        particle.style.height = particle.style.width;
                        particle.style.left = `${Math.random() * 100}%`;
                        particle.style.top = `${Math.random() * 100}%`;
                        particle.style.animationDelay = `${Math.random() * 6}s`;
                        particle.style.animationDuration = `${Math.random() * 4 + 4}s`;
                        heroRef.current.appendChild(particle);
                    }
                };
                createParticles();

                // Matrix rain effect
                const createMatrixRain = () => {
                    const chars = '01';
                    for (let i = 0; i < 20; i++) {
                        const column = document.createElement('div');
                        column.className = 'absolute text-green-400 mono text-xs opacity-20';
                        column.style.left = `${Math.random() * 100}%`;
                        column.style.animationDelay = `${Math.random() * 5}s`;

                        let text = '';
                        for (let j = 0; j < 20; j++) {
                            text += chars[Math.floor(Math.random() * chars.length)] + '<br>';
                        }
                        column.innerHTML = text;

                        gsap.to(column, {
                            y: window.innerHeight + 200,
                            duration: Math.random() * 10 + 10,
                            repeat: -1,
                            ease: 'none'
                        });

                        matrixRef.current.appendChild(column);
                    }
                };
                createMatrixRain();
            }, []);

            return (
                <section className="min-h-screen flex items-center relative overflow-hidden grid-bg" ref={heroRef}>
                    <div className="absolute inset-0" ref={matrixRef}></div>

                    <div className="container mx-auto px-6 relative z-10">
                        <div className="grid md:grid-cols-2 gap-12 items-center">
                            <div className="space-y-8">
                                <div className="space-y-4">
                                    <div className="mono text-green-400 text-sm font-medium tracking-wider">
                                        [ NEURAL NETWORK ACTIVATED ]
                                    </div>
                                    <h1 className="text-5xl md:text-7xl font-bold leading-tight">
                                        <span className="glitch neon-text" data-text="NEXT-GEN">NEXT-GEN</span><br/>
                                        <span className="text-white">AI AUTOMATION</span><br/>
                                        <span className="bg-gradient-to-r from-green-400 to-blue-500 bg-clip-text text-transparent">
                                            UNLEASHED
                                        </span>
                                    </h1>
                                </div>

                                <p className="text-xl text-gray-300 leading-relaxed max-w-lg">
                                    Deploy autonomous AI systems that scale infinitely.
                                    Revolutionary neural networks that learn, adapt, and dominate your market.
                                </p>

                                <div className="flex flex-col sm:flex-row gap-4">
                                    <button className="cyber-glow px-8 py-4 bg-gradient-to-r from-green-400 to-blue-500 text-black font-bold rounded-lg mono text-sm flex items-center justify-center group">
                                        <svg className="w-5 h-5 mr-2 group-hover:animate-spin" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M13 3L4 14h7v7l9-11h-7V3z" />
                                        </svg>
                                        INITIALIZE SYSTEM
                                    </button>
                                    <button className="border border-green-400 text-green-400 px-8 py-4 rounded-lg mono text-sm hover:bg-green-400/10 transition-all duration-300">
                                        VIEW NEURAL MAP
                                    </button>
                                </div>

                                <div className="flex items-center space-x-6 text-sm mono">
                                    <div className="flex items-center space-x-2">
                                        <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                                        <span className="text-gray-400">SYSTEM ONLINE</span>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
                                        <span className="text-gray-400">AI READY</span>
                                    </div>
                                </div>
                            </div>

                            <div className="relative">
                                <div className="w-full h-96 bg-gradient-to-br from-green-400/20 to-blue-500/20 rounded-2xl cyber-glow flex items-center justify-center">
                                    <div className="text-center space-y-4">
                                        <div className="w-24 h-24 bg-gradient-to-r from-green-400 to-blue-500 rounded-full mx-auto flex items-center justify-center animate-pulse">
                                            <svg className="w-12 h-12 text-black" fill="currentColor" viewBox="0 0 24 24">
                                                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" />
                                            </svg>
                                        </div>
                                        <div className="mono text-green-400 text-sm">NEURAL CORE</div>
                                        <div className="text-white text-lg font-bold">PROCESSING...</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            );
        };

        // Solutions Component
        const Solutions = () => {
            const solutionsRef = useRef(null);
            useEffect(() => {
                gsap.from(solutionsRef.current.children, {
                    opacity: 0,
                    y: 80,
                    stagger: 0.2,
                    duration: 1,
                    ease: 'power3.out',
                    scrollTrigger: { trigger: solutionsRef.current, start: 'top 80%' }
                });
            }, []);

            const solutions = [
                {
                    title: 'NEURAL AUTOMATION',
                    desc: 'Self-learning AI systems that evolve with your business needs, automating complex workflows with zero human intervention.',
                    icon: '🧠',
                    code: 'neural.automate()',
                    metrics: '99.7% accuracy'
                },
                {
                    title: 'QUANTUM TARGETING',
                    desc: 'Advanced algorithms that predict customer behavior with quantum-level precision, maximizing conversion rates.',
                    icon: '🎯',
                    code: 'quantum.target()',
                    metrics: '340% ROI boost'
                },
                {
                    title: 'INFINITE SCALING',
                    desc: 'Cloud-native architecture that scales from startup to enterprise without performance degradation.',
                    icon: '♾️',
                    code: 'scale.infinite()',
                    metrics: '10M+ operations/sec'
                }
            ];

            return (
                <section id="solutions" className="py-32 bg-black/50">
                    <div className="container mx-auto px-6">
                        <div className="text-center mb-20" ref={solutionsRef}>
                            <div className="mono text-green-400 text-sm font-medium tracking-wider mb-4">
                                [ SOLUTION MATRIX ]
                            </div>
                            <h2 className="text-5xl md:text-6xl font-bold mb-6">
                                <span className="neon-text">NEURAL</span> SOLUTIONS
                            </h2>
                            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
                                Deploy cutting-edge AI systems that redefine what's possible.
                                Our neural networks don't just automate—they revolutionize.
                            </p>
                        </div>

                        <div className="grid md:grid-cols-3 gap-8">
                            {solutions.map((solution, index) => (
                                <div key={index} className="hover-card bg-gradient-to-br from-gray-900 to-black p-8 rounded-2xl group">
                                    <div className="flex items-center justify-between mb-6">
                                        <span className="text-4xl">{solution.icon}</span>
                                        <div className="mono text-green-400 text-xs bg-green-400/10 px-3 py-1 rounded-full">
                                            {solution.metrics}
                                        </div>
                                    </div>

                                    <h3 className="text-2xl font-bold mb-4 group-hover:text-green-400 transition-colors">
                                        {solution.title}
                                    </h3>

                                    <p className="text-gray-300 mb-6 leading-relaxed">
                                        {solution.desc}
                                    </p>

                                    <div className="bg-black/50 p-4 rounded-lg border border-green-400/20">
                                        <div className="mono text-green-400 text-sm">
                                            <span className="text-gray-500">$</span> {solution.code}
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>

                        <div className="text-center mt-16">
                            <button className="cyber-glow px-10 py-4 bg-gradient-to-r from-green-400 to-blue-500 text-black font-bold rounded-lg mono text-sm">
                                DEPLOY SOLUTIONS
                            </button>
                        </div>
                    </div>
                </section>
            );
        };

        // Technology Stack Component
        const Technology = () => {
            const techRef = useRef(null);
            useEffect(() => {
                gsap.from(techRef.current.children, {
                    opacity: 0,
                    scale: 0.8,
                    stagger: 0.1,
                    duration: 0.8,
                    ease: 'power3.out',
                    scrollTrigger: { trigger: techRef.current, start: 'top 80%' }
                });
            }, []);

            const techStack = [
                { name: 'GPT-4 TURBO', category: 'NEURAL ENGINE', status: 'ACTIVE' },
                { name: 'TENSORFLOW', category: 'ML FRAMEWORK', status: 'OPTIMIZED' },
                { name: 'KUBERNETES', category: 'ORCHESTRATION', status: 'SCALING' },
                { name: 'REDIS CLUSTER', category: 'MEMORY CACHE', status: 'LIGHTNING' },
                { name: 'POSTGRESQL', category: 'DATA LAYER', status: 'SECURED' },
                { name: 'DOCKER SWARM', category: 'CONTAINERS', status: 'DEPLOYED' }
            ];

            return (
                <section id="technology" className="py-32 grid-bg">
                    <div className="container mx-auto px-6">
                        <div className="text-center mb-20">
                            <div className="mono text-green-400 text-sm font-medium tracking-wider mb-4">
                                [ TECH STACK ANALYSIS ]
                            </div>
                            <h2 className="text-5xl md:text-6xl font-bold mb-6">
                                <span className="bg-gradient-to-r from-green-400 to-blue-500 bg-clip-text text-transparent">
                                    QUANTUM
                                </span> ARCHITECTURE
                            </h2>
                            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
                                Built on bleeding-edge technology that pushes the boundaries of what AI can achieve.
                                Every component optimized for maximum performance and infinite scalability.
                            </p>
                        </div>

                        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6" ref={techRef}>
                            {techStack.map((tech, index) => (
                                <div key={index} className="bg-black/60 border border-green-400/20 rounded-xl p-6 hover-card group">
                                    <div className="flex items-center justify-between mb-4">
                                        <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                                        <span className="mono text-xs text-green-400 bg-green-400/10 px-2 py-1 rounded">
                                            {tech.status}
                                        </span>
                                    </div>

                                    <h3 className="text-xl font-bold mb-2 group-hover:text-green-400 transition-colors">
                                        {tech.name}
                                    </h3>

                                    <p className="mono text-sm text-gray-400">
                                        {tech.category}
                                    </p>

                                    <div className="mt-4 h-1 bg-gray-800 rounded-full overflow-hidden">
                                        <div className="h-full bg-gradient-to-r from-green-400 to-blue-500 rounded-full animate-pulse"
                                             style={{width: `${Math.random() * 40 + 60}%`}}></div>
                                    </div>
                                </div>
                            ))}
                        </div>

                        <div className="mt-20 text-center">
                            <div className="bg-black/80 border border-green-400/30 rounded-2xl p-8 max-w-4xl mx-auto">
                                <div className="mono text-green-400 text-sm mb-4">SYSTEM PERFORMANCE</div>
                                <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
                                    <div className="text-center">
                                        <div className="text-3xl font-bold text-white mb-2">99.99%</div>
                                        <div className="mono text-xs text-gray-400">UPTIME</div>
                                    </div>
                                    <div className="text-center">
                                        <div className="text-3xl font-bold text-white mb-2">&lt;10ms</div>
                                        <div className="mono text-xs text-gray-400">LATENCY</div>
                                    </div>
                                    <div className="text-center">
                                        <div className="text-3xl font-bold text-white mb-2">1M+</div>
                                        <div className="mono text-xs text-gray-400">OPS/SEC</div>
                                    </div>
                                    <div className="text-center">
                                        <div className="text-3xl font-bold text-white mb-2">∞</div>
                                        <div className="mono text-xs text-gray-400">SCALE</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            );
        };

        // Showcase Component
        const Showcase = () => {
            const showcaseRef = useRef(null);
            useEffect(() => {
                gsap.from(showcaseRef.current.children, {
                    opacity: 0,
                    y: 60,
                    stagger: 0.2,
                    duration: 1,
                    ease: 'power3.out',
                    scrollTrigger: { trigger: showcaseRef.current, start: 'top 80%' }
                });
            }, []);

            const projects = [
                {
                    title: 'QUANTUM LEAD ENGINE',
                    description: 'AI system that generated 50K+ qualified leads in 30 days',
                    metrics: { conversion: '47%', revenue: '$2.3M', time: '72hrs' },
                    status: 'DEPLOYED'
                },
                {
                    title: 'NEURAL CRM FUSION',
                    description: 'Autonomous customer relationship management with predictive analytics',
                    metrics: { efficiency: '340%', accuracy: '99.7%', cost: '-80%' },
                    status: 'ACTIVE'
                },
                {
                    title: 'INFINITY SCALE PROTOCOL',
                    description: 'Self-replicating automation systems that scale without limits',
                    metrics: { scale: '∞', uptime: '99.99%', speed: '10ms' },
                    status: 'EVOLVING'
                }
            ];

            return (
                <section id="showcase" className="py-32 bg-black/50">
                    <div className="container mx-auto px-6">
                        <div className="text-center mb-20">
                            <div className="mono text-green-400 text-sm font-medium tracking-wider mb-4">
                                [ PROJECT SHOWCASE ]
                            </div>
                            <h2 className="text-5xl md:text-6xl font-bold mb-6">
                                NEURAL <span className="neon-text">VICTORIES</span>
                            </h2>
                            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
                                Real deployments. Real results. Real revolution.
                                See how our AI systems are reshaping entire industries.
                            </p>
                        </div>

                        <div className="space-y-8" ref={showcaseRef}>
                            {projects.map((project, index) => (
                                <div key={index} className="bg-gradient-to-r from-gray-900 to-black border border-green-400/20 rounded-2xl p-8 hover-card group">
                                    <div className="grid md:grid-cols-3 gap-8 items-center">
                                        <div className="md:col-span-2">
                                            <div className="flex items-center gap-4 mb-4">
                                                <h3 className="text-2xl font-bold group-hover:text-green-400 transition-colors">
                                                    {project.title}
                                                </h3>
                                                <span className="mono text-xs bg-green-400/10 text-green-400 px-3 py-1 rounded-full">
                                                    {project.status}
                                                </span>
                                            </div>
                                            <p className="text-gray-300 text-lg mb-6">
                                                {project.description}
                                            </p>
                                            <div className="flex flex-wrap gap-4">
                                                {Object.entries(project.metrics).map(([key, value]) => (
                                                    <div key={key} className="bg-black/50 px-4 py-2 rounded-lg border border-green-400/20">
                                                        <div className="mono text-green-400 text-sm">{key.toUpperCase()}</div>
                                                        <div className="text-white font-bold">{value}</div>
                                                    </div>
                                                ))}
                                            </div>
                                        </div>
                                        <div className="flex justify-center">
                                            <div className="w-32 h-32 bg-gradient-to-br from-green-400/20 to-blue-500/20 rounded-2xl cyber-glow flex items-center justify-center">
                                                <svg className="w-16 h-16 text-green-400 animate-pulse" fill="currentColor" viewBox="0 0 24 24">
                                                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" />
                                                </svg>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>

                        <div className="text-center mt-16">
                            <button className="cyber-glow px-10 py-4 bg-gradient-to-r from-green-400 to-blue-500 text-black font-bold rounded-lg mono text-sm">
                                VIEW ALL PROJECTS
                            </button>
                        </div>
                    </div>
                </section>
            );
        };

        // Metrics Component
        const Metrics = () => {
            const metricsRef = useRef(null);
            const [counters, setCounters] = useState([0, 0, 0, 0]);

            useEffect(() => {
                gsap.from(metricsRef.current.children, {
                    opacity: 0,
                    scale: 0.8,
                    stagger: 0.1,
                    duration: 1,
                    ease: 'power3.out',
                    scrollTrigger: {
                        trigger: metricsRef.current,
                        start: 'top 80%',
                        onEnter: () => {
                            // Animate counters
                            const targets = [1000000, 99.9, 847, 24];
                            targets.forEach((target, index) => {
                                gsap.to({}, {
                                    duration: 2,
                                    ease: 'power2.out',
                                    onUpdate: function() {
                                        const progress = this.progress();
                                        setCounters(prev => {
                                            const newCounters = [...prev];
                                            newCounters[index] = Math.floor(target * progress);
                                            return newCounters;
                                        });
                                    }
                                });
                            });
                        }
                    }
                });
            }, []);

            const metrics = [
                { value: counters[0], suffix: '+', label: 'NEURAL OPERATIONS', unit: '/second' },
                { value: counters[1], suffix: '%', label: 'SYSTEM ACCURACY', unit: 'precision' },
                { value: counters[2], suffix: 'TB', label: 'DATA PROCESSED', unit: 'daily' },
                { value: 24, suffix: '/7', label: 'QUANTUM UPTIME', unit: 'always on' }
            ];

            return (
                <section id="metrics" className="py-32 grid-bg">
                    <div className="container mx-auto px-6">
                        <div className="text-center mb-20">
                            <div className="mono text-green-400 text-sm font-medium tracking-wider mb-4">
                                [ PERFORMANCE METRICS ]
                            </div>
                            <h2 className="text-5xl md:text-6xl font-bold mb-6">
                                QUANTUM <span className="neon-text">ANALYTICS</span>
                            </h2>
                            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
                                Real-time performance data from our neural networks.
                                These numbers represent the future of AI automation.
                            </p>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16" ref={metricsRef}>
                            {metrics.map((metric, index) => (
                                <div key={index} className="bg-black/60 border border-green-400/20 rounded-2xl p-8 text-center hover-card group">
                                    <div className="mb-6">
                                        <div className="text-4xl md:text-5xl font-bold text-white mb-2 mono">
                                            {metric.value.toLocaleString()}{metric.suffix}
                                        </div>
                                        <div className="h-1 bg-gradient-to-r from-green-400 to-blue-500 rounded-full mx-auto w-16 group-hover:w-full transition-all duration-500"></div>
                                    </div>
                                    <h3 className="text-lg font-bold mb-2 group-hover:text-green-400 transition-colors">
                                        {metric.label}
                                    </h3>
                                    <p className="mono text-sm text-gray-400">
                                        {metric.unit}
                                    </p>
                                </div>
                            ))}
                        </div>

                        <div className="bg-black/80 border border-green-400/30 rounded-2xl p-8">
                            <div className="grid md:grid-cols-2 gap-8 items-center">
                                <div>
                                    <h3 className="text-2xl font-bold mb-4">NEURAL NETWORK STATUS</h3>
                                    <div className="space-y-4">
                                        <div className="flex justify-between items-center">
                                            <span className="mono text-sm">CPU CORES</span>
                                            <span className="text-green-400">128 ACTIVE</span>
                                        </div>
                                        <div className="flex justify-between items-center">
                                            <span className="mono text-sm">MEMORY USAGE</span>
                                            <span className="text-green-400">847TB ALLOCATED</span>
                                        </div>
                                        <div className="flex justify-between items-center">
                                            <span className="mono text-sm">NETWORK LATENCY</span>
                                            <span className="text-green-400">&lt;10MS GLOBAL</span>
                                        </div>
                                        <div className="flex justify-between items-center">
                                            <span className="mono text-sm">AI MODELS</span>
                                            <span className="text-green-400">47 DEPLOYED</span>
                                        </div>
                                    </div>
                                </div>
                                <div className="text-center">
                                    <div className="w-32 h-32 bg-gradient-to-br from-green-400/20 to-blue-500/20 rounded-full cyber-glow flex items-center justify-center mx-auto mb-4">
                                        <div className="w-16 h-16 bg-green-400 rounded-full animate-pulse"></div>
                                    </div>
                                    <div className="mono text-green-400 text-sm">SYSTEM STATUS: OPTIMAL</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            );
        };

        // Contact Component
        const Contact = () => {
            const contactRef = useRef(null);
            useEffect(() => {
                gsap.from(contactRef.current.children, {
                    opacity: 0,
                    y: 50,
                    stagger: 0.2,
                    duration: 1,
                    ease: 'power3.out',
                    scrollTrigger: { trigger: contactRef.current, start: 'top 80%' }
                });
            }, []);

            return (
                <section id="contact" className="py-32 bg-black/50">
                    <div className="container mx-auto px-6">
                        <div className="max-w-4xl mx-auto">
                            <div className="text-center mb-16" ref={contactRef}>
                                <div className="mono text-green-400 text-sm font-medium tracking-wider mb-4">
                                    [ NEURAL INTERFACE READY ]
                                </div>
                                <h2 className="text-5xl md:text-6xl font-bold mb-6">
                                    <span className="glitch neon-text" data-text="DEPLOY">DEPLOY</span> YOUR AI
                                </h2>
                                <p className="text-xl text-gray-300 max-w-2xl mx-auto">
                                    Ready to revolutionize your business with quantum-powered AI?
                                    Initialize your neural network deployment today.
                                </p>
                            </div>

                            <div className="grid md:grid-cols-2 gap-12 items-center">
                                <div className="space-y-8">
                                    <div className="bg-black/60 border border-green-400/20 rounded-2xl p-8">
                                        <h3 className="text-2xl font-bold mb-6">SYSTEM REQUIREMENTS</h3>
                                        <div className="space-y-4">
                                            <div className="flex justify-between items-center">
                                                <span className="mono text-sm">DEPLOYMENT TIME</span>
                                                <span className="text-green-400">24 HOURS</span>
                                            </div>
                                            <div className="flex justify-between items-center">
                                                <span className="mono text-sm">INTEGRATION</span>
                                                <span className="text-green-400">SEAMLESS</span>
                                            </div>
                                            <div className="flex justify-between items-center">
                                                <span className="mono text-sm">SUPPORT LEVEL</span>
                                                <span className="text-green-400">QUANTUM</span>
                                            </div>
                                            <div className="flex justify-between items-center">
                                                <span className="mono text-sm">SCALABILITY</span>
                                                <span className="text-green-400">INFINITE</span>
                                            </div>
                                        </div>
                                    </div>

                                    <div className="space-y-4">
                                        <button className="w-full cyber-glow px-8 py-4 bg-gradient-to-r from-green-400 to-blue-500 text-black font-bold rounded-lg mono text-sm flex items-center justify-center group">
                                            <svg className="w-5 h-5 mr-2 group-hover:animate-spin" fill="currentColor" viewBox="0 0 24 24">
                                                <path d="M13 3L4 14h7v7l9-11h-7V3z" />
                                            </svg>
                                            INITIALIZE DEPLOYMENT
                                        </button>
                                        <button className="w-full border border-green-400 text-green-400 px-8 py-4 rounded-lg mono text-sm hover:bg-green-400/10 transition-all duration-300">
                                            SCHEDULE NEURAL CONSULTATION
                                        </button>
                                    </div>
                                </div>

                                <div className="bg-gradient-to-br from-gray-900 to-black border border-green-400/20 rounded-2xl p-8">
                                    <div className="space-y-6">
                                        <input
                                            type="text"
                                            placeholder="NEURAL ID"
                                            className="w-full bg-black/50 border border-green-400/20 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:border-green-400 focus:outline-none mono text-sm"
                                        />
                                        <input
                                            type="email"
                                            placeholder="QUANTUM EMAIL"
                                            className="w-full bg-black/50 border border-green-400/20 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:border-green-400 focus:outline-none mono text-sm"
                                        />
                                        <input
                                            type="text"
                                            placeholder="ORGANIZATION"
                                            className="w-full bg-black/50 border border-green-400/20 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:border-green-400 focus:outline-none mono text-sm"
                                        />
                                        <textarea
                                            placeholder="DEPLOYMENT REQUIREMENTS"
                                            rows="4"
                                            className="w-full bg-black/50 border border-green-400/20 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:border-green-400 focus:outline-none mono text-sm resize-none"
                                        ></textarea>
                                        <button className="w-full cyber-glow px-6 py-3 bg-gradient-to-r from-green-400 to-blue-500 text-black font-bold rounded-lg mono text-sm">
                                            TRANSMIT DATA
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            );
        };

        // Footer Component
        const Footer = () => (
            <footer className="py-16 bg-black border-t border-green-400/20">
                <div className="container mx-auto px-6">
                    <div className="grid md:grid-cols-4 gap-8 mb-12">
                        <div className="space-y-4">
                            <div className="flex items-center space-x-4">
                                <div className="w-10 h-10 bg-gradient-to-r from-green-400 to-blue-500 rounded-lg flex items-center justify-center cyber-glow">
                                    <svg className="w-6 h-6 text-black font-bold" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M13 3L4 14h7v7l9-11h-7V3z" />
                                    </svg>
                                </div>
                                <div>
                                    <div className="text-xl font-bold neon-text mono">NeuralFlow</div>
                                    <div className="text-xs text-green-400 mono">AI SOLUTIONS</div>
                                </div>
                            </div>
                            <p className="text-gray-400 text-sm">
                                Quantum-powered AI systems that revolutionize business automation.
                            </p>
                        </div>

                        <div>
                            <h4 className="text-white font-bold mb-4 mono">SOLUTIONS</h4>
                            <div className="space-y-2 text-sm">
                                <a href="#" className="block text-gray-400 hover:text-green-400 transition-colors">Neural Automation</a>
                                <a href="#" className="block text-gray-400 hover:text-green-400 transition-colors">Quantum Targeting</a>
                                <a href="#" className="block text-gray-400 hover:text-green-400 transition-colors">Infinite Scaling</a>
                                <a href="#" className="block text-gray-400 hover:text-green-400 transition-colors">AI Analytics</a>
                            </div>
                        </div>

                        <div>
                            <h4 className="text-white font-bold mb-4 mono">RESOURCES</h4>
                            <div className="space-y-2 text-sm">
                                <a href="#" className="block text-gray-400 hover:text-green-400 transition-colors">Documentation</a>
                                <a href="#" className="block text-gray-400 hover:text-green-400 transition-colors">API Reference</a>
                                <a href="#" className="block text-gray-400 hover:text-green-400 transition-colors">Neural Blog</a>
                                <a href="#" className="block text-gray-400 hover:text-green-400 transition-colors">Support Portal</a>
                            </div>
                        </div>

                        <div>
                            <h4 className="text-white font-bold mb-4 mono">CONNECT</h4>
                            <div className="space-y-2 text-sm">
                                <a href="#" className="block text-gray-400 hover:text-green-400 transition-colors">Neural Network</a>
                                <a href="#" className="block text-gray-400 hover:text-green-400 transition-colors">Quantum Discord</a>
                                <a href="#" className="block text-gray-400 hover:text-green-400 transition-colors">AI Twitter</a>
                                <a href="#" className="block text-gray-400 hover:text-green-400 transition-colors">GitHub</a>
                            </div>
                        </div>
                    </div>

                    <div className="border-t border-green-400/20 pt-8">
                        <div className="flex flex-col md:flex-row justify-between items-center">
                            <p className="mono text-gray-400 text-sm">
                                © 2025 NeuralFlow. All neural networks reserved.
                            </p>
                            <div className="flex space-x-6 mt-4 md:mt-0">
                                <a href="#" className="text-gray-400 hover:text-green-400 transition-colors text-sm">Privacy</a>
                                <a href="#" className="text-gray-400 hover:text-green-400 transition-colors text-sm">Terms</a>
                                <a href="#" className="text-gray-400 hover:text-green-400 transition-colors text-sm">Security</a>
                            </div>
                        </div>
                    </div>
                </div>
            </footer>
        );

        // Main App Component
        const App = () => {
            return (
                <div className="dark-gradient">
                    <Nav />
                    <Hero />
                    <Solutions />
                    <Technology />
                    <Showcase />
                    <Metrics />
                    <Contact />
                    <Footer />
                </div>
            );
        };

        ReactDOM.render(<App />, document.getElementById('root'));
    </script>
</body>
</html>
